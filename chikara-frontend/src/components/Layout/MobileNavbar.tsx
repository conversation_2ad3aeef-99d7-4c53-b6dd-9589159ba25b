// import eventsImg from "@/assets/icons/navitems/events.png";
// import leaderboardsImg from "@/assets/icons/navitems/leaderboards.png";
// import messagesImg from "@/assets/icons/navitems/messages.png";
import { Popover, PopoverButton } from "@headlessui/react";
import clsx from "clsx";
import { Link } from "react-router-dom";
import useGetUnreadNotifications from "@/hooks/api/useGetUnreadNotifications";
import useGetUnreadMessages from "@/hooks/api/useGetUnreadMessages";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { DisplayAvatar } from "@/components/DisplayAvatar";
import NotificationBadge from "@/components/NotificationBadge";
import MobileNavMenu from "./MobileNavMenu";
import MobileStatsNav from "./MobileStatsNav";
import expImg from "@/assets/icons/UI/expBG.png";
import newsPosts from "../../constants/news";

const LevelDisplay = ({ icon, level }: { icon: string; level: number }) => (
    <div className="-bottom-1 -right-3 xs:-right-4 absolute size-6 xs:size-8">
        <div className="relative h-5 xs:h-6 w-4 xs:w-5">
            <img className="size-full" src={icon} alt="" />
            <p className="-translate-x-1/2 -translate-y-1/2 absolute top-[45%] left-1/2 font-bold font-body text-stroke-sm text-white text-[10px] xs:text-xs dark:text-stroke-s-sm">
                {level}
            </p>
        </div>
    </div>
);

export default function GameMenu() {
    const { data: currentUser, isLoading, error } = useFetchCurrentUser();
    const { data: unreadNotifications } = useGetUnreadNotifications();
    const { data: unreadMessages } = useGetUnreadMessages();

    const percentEXP =
        currentUser?.xp && currentUser?.xpForNextLevel
            ? Math.min(Math.max(currentUser.xp / currentUser.xpForNextLevel, 0), 1)
            : 0;

    // User status
    const hospitalised = currentUser?.hospitalisedUntil !== null;
    const jailed = currentUser?.jailedUntil !== null;

    // News notifications
    const lastNewsIDRead = currentUser?.lastNewsIDRead;
    const unreadNewsPosts = lastNewsIDRead
        ? newsPosts[0].id - lastNewsIDRead > 0
            ? newsPosts[0].id - lastNewsIDRead
            : 0
        : 0;

    // Notifications
    const hasNotifications = (unreadNotifications && unreadNotifications.unread > 0) || unreadNewsPosts > 0;

    if (error) throw new Error("Server Offline");

    return (
        <>
            <Popover
                as="header"
                className={clsx(
                    "border-b bg-linear-to-b from-slate-800 to-gray-900 z-200 h-12 xs:h-14 w-full border-gray-800 text-shadow md:h-[80px] dark:border-gray-800"
                )}
            >
                {() => (
                    <>
                        {isLoading ? null : (
                            <>
                                {/* Top Bar */}
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <div className="relative">
                                            <Link
                                                className="relative flex h-full w-12 xs:w-14 px-1.5 xs:px-2 py-1 xs:py-1.5"
                                                to={`/profile/${currentUser?.id}`}
                                            >
                                                <DisplayAvatar
                                                    src={currentUser}
                                                    className={clsx(
                                                        "my-auto size-9 xs:size-11 rounded-full border border-slate-900 bg-slate-800",
                                                        hospitalised || jailed ? "grayscale" : ""
                                                    )}
                                                />
                                            </Link>
                                            {/* <div className="-bottom-1 -right-0 absolute flex size-5 items-center justify-center rounded-full border border-purple-500 bg-black font-bold text-white text-xs">
                            {currentUser?.level}
                        </div> */}
                                            <LevelDisplay icon={expImg} level={currentUser?.level} />
                                        </div>
                                        {/* <div>
                                            <h3 className="font-bold text-white leading-tight">DarkKnight</h3>
                                            <p className="text-purple-300 text-xs">Warrior</p>
                                        </div> */}
                                    </div>

                                    <div className="absolute top-1 xs:top-1.5 right-1.5 xs:right-2 shrink-0 md:hidden">
                                        <PopoverButton className="group relative z-50 inline-flex h-9 xs:h-11 w-10 xs:w-[2.93rem] items-center justify-center rounded-md">
                                            {hasNotifications && (
                                                <NotificationBadge
                                                    empty
                                                    className="absolute top-0.5 right-0.5 size-2.5 border border-black"
                                                />
                                            )}
                                            <span className="sr-only">Open main menu</span>
                                            <img
                                                className="size-full"
                                                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/nBTwykr.png`}
                                                alt=""
                                            />
                                            <img
                                                className="absolute block h-1/2 w-auto group-active:scale-[0.8]"
                                                aria-hidden="true"
                                                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/rjpWoLn.png`}
                                                alt="menu"
                                            />
                                        </PopoverButton>
                                    </div>
                                    <MobileNavMenu
                                        currentUser={currentUser}
                                        percentEXP={(percentEXP * 100).toString()}
                                        unreadNotifications={unreadNotifications}
                                        unreadMessages={unreadMessages}
                                        statusEffects={statusEffects}
                                        unreadNewsPosts={unreadNewsPosts}
                                    />
                                </div>
                            </>
                        )}
                    </>
                )}
            </Popover>
            <MobileStatsNav currentUser={currentUser} />
        </>
    );
}
