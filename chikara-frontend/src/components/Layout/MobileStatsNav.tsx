import { User } from "@/types/user";
import clsx from "clsx";

import { Activity, Heart, Zap } from "lucide-react";

const MAX_ENERGY = 100;

// import hpImg from "@/assets/icons/UI/HPicon.png";
// import energyImg from "@/assets/icons/UI/energyicon.png";
// import apImg from "@/assets/icons/UI/APicon3.png";

export default function MobileStatsNav({ currentUser }: { currentUser: User }) {
    return (
        <div className="flex items-center gap-0.5 xs:gap-1 bg-linear-to-b from-slate-800 to-gray-900 px-1.5 xs:px-2 py-0.5 xs:py-1 font-accent border-b border-gray-800">
            {/* Health */}
            <div className="flex items-center gap-0.5 xs:gap-1 px-1 xs:px-1.5 py-0.5">
                <Heart className="size-2.5 xs:size-3 text-red-500" />
                <span className="font-medium text-red-300 text-[10px] xs:text-xs">
                    {Math.round((currentUser?.currentHealth / currentUser?.health) * 100)}%
                </span>
            </div>

            {/* Health Bar */}
            <div className="mx-0.5 xs:mx-1 h-1.5 xs:h-2 flex-1 overflow-hidden bg-gray-800 rounded-sm">
                <div
                    className="h-full rounded-xs bg-linear-to-r from-red-800 to-red-500"
                    style={{ width: `${(currentUser?.currentHealth / currentUser?.health) * 100}%` }}
                ></div>
            </div>

            {/* Energy */}
            <div className="flex items-center gap-0.5 xs:gap-1 rounded-full px-1 xs:px-1.5 py-0.5">
                <Zap className="size-2.5 xs:size-3 text-purple-500" />
                <span className="font-medium text-purple-300 text-[10px] xs:text-xs">{currentUser?.energy}</span>
            </div>

            {/* Action Points */}
            <div className="flex items-center gap-0.5 xs:gap-1 rounded-full px-1 xs:px-1.5 py-0.5">
                <Activity className="size-2.5 xs:size-3 text-emerald-500" />
                <div className="flex gap-0.5">
                    {Array.from({ length: currentUser?.maxActionPoints }).map((_, index) => (
                        <div
                            key={index}
                            className={clsx(
                                "h-1.5 xs:h-2 w-0.5 xs:w-1 rounded-xs",
                                index < currentUser?.actionPoints ? "bg-emerald-500" : "bg-gray-600"
                            )}
                        ></div>
                    ))}
                </div>
            </div>
        </div>
    );
}
