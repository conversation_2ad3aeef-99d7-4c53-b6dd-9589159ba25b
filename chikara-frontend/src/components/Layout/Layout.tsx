import { usePersistStore, useSessionStore } from "@/app/store/stores";
import ErrorBoundary from "@/components/Layout/ErrorBoundary";
import LevelupModal from "@/components/Modal/LevelupModal";
import LoadingSpinner from "@/components/Spinners/Spinner";
import TechnicalError from "@/components/TechnicalError";
import { capitaliseFirstLetter } from "@/helpers/capitaliseFirstLetter";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { motion } from "framer-motion";
import { Suspense } from "react";
import { Outlet, useLocation } from "react-router-dom";
import Chatbox from "../../features/chat/Chatbox";
import AlphaDisclaimer from "../../pages/AlphaDisclaimer";
import PageBanner from "./Banners/PageBanner";
import MobileBottomNav from "./MobileBottomNav";
import MobileNavbar from "./MobileNavbar";
import Navbar from "./Navbar";
import SideBar from "./Sidebar/SideBar";

const EXCLUDED_MOTION_ROUTES = [
    "/talents/strength",
    "/talents/defence",
    "/talents/intelligence",
    "/talents/dexterity",
    "/talents/endurance",
    "/equipment",
    "/inventory",
    "/inbox",
    "/news",
];

interface LayoutProps {
    pathname: string;
}

const isExcludedRoute = (path: string): boolean => {
    return EXCLUDED_MOTION_ROUTES.some((route) => path.startsWith(route));
};

const ContentWithMotion: React.FC<React.PropsWithChildren<{ pathname: string }>> = ({ children, pathname }) => {
    if (isExcludedRoute(pathname)) return children;

    return (
        <motion.div
            key={pathname}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="h-full"
        >
            {children}
        </motion.div>
    );
};

const MobileLayout: React.FC<LayoutProps> = ({ pathname }) => {
    return (
        <div className="flex flex-col overflow-hidden h-screen">
            <MobileNavbar />
            <main className="flex-1 overflow-y-auto overflow-x-hidden">
                <ErrorBoundary>
                    <Suspense
                        fallback={
                            <div className="size-full flex items-center justify-center bg-[#111521]">
                                <LoadingSpinner />
                            </div>
                        }
                    >
                        <div className="max-w-4xl mx-auto h-full">
                            <PageBanner />
                            <ContentWithMotion pathname={pathname}>
                                <div className="p-1.5 xs:p-2 h-full">
                                    <Outlet />
                                </div>
                            </ContentWithMotion>
                        </div>
                    </Suspense>
                </ErrorBoundary>
            </main>
            {/* <MobileBottomNavGrid  /> */}
            <MobileBottomNav />
        </div>
    );
};

const DesktopLayout: React.FC<LayoutProps> = ({ pathname }) => {
    return (
        <div className="relative md:flex md:flex-col h-screen">
            <Navbar />
            <div className="flex flex-1 overflow-hidden">
                <SideBar />
                <ErrorBoundary>
                    <Suspense
                        fallback={
                            <div className="size-full flex items-center justify-center bg-[#111521]">
                                <LoadingSpinner />
                            </div>
                        }
                    >
                        <div className="flex-1 flex flex-col border-l-2 dark:border-[#292c3e] overflow-y-auto overflow-x-hidden">
                            <PageBanner />
                            <main className="flex-1 p-4">
                                <div className="size-full mx-auto">
                                    <Outlet />
                                </div>
                            </main>
                        </div>
                    </Suspense>
                </ErrorBoundary>
                {pathname !== "/chat" && <Chatbox isMainChat chatRoom={{ id: 1, name: "global" }} />}
            </div>
        </div>
    );
};

export default function Layout() {
    const isMobile = useCheckMobileScreen();
    const location = useLocation();
    const { levelupValue, setLevelupValue } = useSessionStore();
    const { alphaDisclaimerComplete } = usePersistStore();

    if (location.pathname === "/error") return <TechnicalError />;

    return (
        <div className="min-h-screen text-stroke-sm">
            <title>{`${capitaliseFirstLetter(location.pathname.split("/").pop())} | Chikara Academy MMO`}</title>
            {/* Modals and overlays */}
            {levelupValue > 1 && <LevelupModal newLevel={levelupValue} setLevelupValue={setLevelupValue} />}
            {!alphaDisclaimerComplete && <AlphaDisclaimer />}

            {/* Main content area */}
            {isMobile ? <MobileLayout pathname={location.pathname} /> : <DesktopLayout pathname={location.pathname} />}
        </div>
    );
}
