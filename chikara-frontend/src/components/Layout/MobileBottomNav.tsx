import mobileNavBG from "@/assets/icons/UI/mobileNavBG.png";
import mobileNavBGDark from "@/assets/icons/UI/mobileNavBGDark.png";
import arrow from "@/assets/images/UI/arrow.gif";
import NotificationBadge from "@/components/NotificationBadge";
import StrokedText from "@/components/StrokedText";
import useGetAvailableQuestList from "@/features/tasks/api/useGetAvailableQuestList";
import { mobileNavItems } from "@/helpers/navItems";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { cn } from "@/lib/utils";
import { Fragment } from "react";
import { Link, useLocation, Location } from "react-router-dom";
import { useNormalStore } from "../../app/store/stores";

const EXCLUDED_PAGES = ["/fight"];

interface NavButtonProps {
    url: string;
    text: string;
    image: string;
    current: boolean;
    isIncapacitated: boolean;
    inFight?: string | null;
    availableQuests?: number;
    unreadChatMessages: number;
    displayTutorialArrow?: boolean;
    isDisabled: boolean;
    craftCollectReady: boolean;
    location: Location;
}

const NavButton = ({
    url,
    text,
    image,
    current,
    isIncapacitated,
    inFight,
    availableQuests,
    unreadChatMessages,
    displayTutorialArrow = false,
    isDisabled,
    craftCollectReady,
}: NavButtonProps) => {
    let disabled = isDisabled;
    if (isIncapacitated) {
        if (text === "Explore" || text === "Adventure") {
            disabled = true;
        }
    }
    if (inFight) {
        disabled = true;
    }
    const tutorialArrow = text === "Tasks" && !current && displayTutorialArrow;

    const link = disabled ? "#" : url;
    return (
        <Link
            to={link}
            className={cn("relative mb-0.5 xs:mb-1 w-1/5 px-0", current ? "-mt-3 xs:-mt-4" : "-mt-1.5 xs:-mt-2")}
        >
            {tutorialArrow && (
                <img
                    className="-rotate-90 -translate-x-1/2 -top-12 xs:-top-16 absolute left-1/2 size-20 xs:size-24 scale-75"
                    src={arrow}
                    alt=""
                />
            )}

            <button
                type="button"
                className={cn("flex size-full flex-col items-center justify-center", disabled ? "grayscale" : "")}
            >
                <div className="relative size-full">
                    {text === "Explore" && craftCollectReady && (
                        <NotificationBadge
                            empty
                            pulse
                            className="top-3! right-[15%]! text-xs! aspect-square! h-[1.15rem]! absolute border border-black"
                        />
                    )}

                    {text === "Tasks" && availableQuests ? (
                        <NotificationBadge
                            amount={availableQuests}
                            className="top-3! right-[12%]! text-xs! w-[1.35rem]! h-[1.35rem]! absolute border border-black"
                        />
                    ) : null}

                    {text === "Chat" && unreadChatMessages > 0 && (
                        <NotificationBadge
                            amount={unreadChatMessages}
                            className="top-3! right-[12%]! text-xs! w-[1.35rem]! h-[1.35rem]! absolute border border-black"
                        />
                    )}
                    <img
                        src={image}
                        alt=""
                        className={cn(
                            "mx-auto mb-0.5 h-8 xs:h-[2.4rem] sm:h-10 w-auto",
                            current ? "mt-3 xs:mt-[1.3rem] scale-[110%] xs:scale-[120%]" : "mt-3 xs:mt-4"
                        )}
                    />

                    <img
                        className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2 z-[-5] max-h-full"
                        src={current ? mobileNavBG : mobileNavBGDark}
                        alt=""
                    />
                </div>
                {current && (
                    <span className="absolute top-[60%] font-accent text-custom-yellow text-sm xs:text-lg text-stroke-s-md uppercase">
                        <StrokedText>{text}</StrokedText>
                    </span>
                )}
            </button>
        </Link>
    );
};

export default function MobileBottomNav() {
    const location = useLocation();
    const { data: availableQuests } = useGetAvailableQuestList();
    const { data: currentUser } = useFetchCurrentUser();
    const { unreadChatMessages, craftCollectReady, preventNavigation } = useNormalStore();

    const hospitalised = (currentUser?.hospitalisedUntil ?? 0) > 0;
    const jailed = (currentUser?.jailedUntil ?? 0) > 0;

    const navigation = mobileNavItems(hospitalised, jailed);

    if (EXCLUDED_PAGES.includes(location.pathname)) return null;

    return (
        <div className="z-200 h-16 xs:h-20 w-full border-gray-600 border-t bg-linear-to-t from-blue-700 to-blue-800 dark:from-slate-700 dark:to-slate-900">
            <div className="flex h-full max-w-lg flex-row justify-center gap-1 xs:gap-1.5 sm:gap-0 font-medium md:max-w-none">
                {navigation.map((navItem) => (
                    <Fragment key={navItem.name}>
                        <NavButton
                            isDisabled={preventNavigation}
                            url={navItem.href}
                            text={navItem.name}
                            image={navItem.icon}
                            current={navItem.href === location.pathname}
                            isIncapacitated={hospitalised || jailed}
                            inFight={currentUser?.battleValidUntil}
                            availableQuests={availableQuests?.data?.length}
                            unreadChatMessages={unreadChatMessages}
                            location={location}
                            craftCollectReady={craftCollectReady}
                        />
                    </Fragment>
                ))}
            </div>
        </div>
    );
}
